# coding=utf-8
"""
Tests for deadlock handling and retry mechanisms.
"""
import asyncio
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from asyncpg.exceptions import DeadlockDetectedError
from psycopg2.errors import DeadlockDetected

from dags.data_pipeline.utility_code import (
    handle_deadlock_with_retry,
    deadlock_retry,
    detect_deadlocks,
    kill_transaction,
    MaxRetriesExceededError
)


class TestDeadlockHandling:
    """Test deadlock detection and handling functionality."""

    @pytest.mark.asyncio
    async def test_handle_deadlock_with_retry_no_deadlocks(self):
        """Test deadlock handler when no deadlocks are present."""
        with patch('dags.data_pipeline.utility_code.detect_deadlocks') as mock_detect:
            mock_detect.return_value = []

            result = await handle_deadlock_with_retry("test_operation")

            assert result['deadlocks_found'] == 0
            assert result['deadlocks_killed'] == 0
            assert result['success'] is True
            mock_detect.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_deadlock_with_retry_with_deadlocks(self):
        """Test deadlock handler when deadlocks are present and successfully killed."""
        with patch('dags.data_pipeline.utility_code.detect_deadlocks') as mock_detect, \
             patch('dags.data_pipeline.utility_code.kill_transaction') as mock_kill:

            mock_detect.return_value = [12345, 67890]
            mock_kill.return_value = None

            result = await handle_deadlock_with_retry("test_operation")

            assert result['deadlocks_found'] == 2
            assert result['deadlocks_killed'] == 2
            assert result['success'] is True
            mock_detect.assert_called_once()
            assert mock_kill.call_count == 2

    @pytest.mark.asyncio
    async def test_handle_deadlock_with_retry_kill_failure(self):
        """Test deadlock handler when killing transactions fails."""
        with patch('dags.data_pipeline.utility_code.detect_deadlocks') as mock_detect, \
             patch('dags.data_pipeline.utility_code.kill_transaction') as mock_kill:

            mock_detect.return_value = [12345, 67890]
            mock_kill.side_effect = [None, Exception("Kill failed")]

            result = await handle_deadlock_with_retry("test_operation")

            assert result['deadlocks_found'] == 2
            assert result['deadlocks_killed'] == 1  # Only first one succeeded
            assert result['success'] is True  # Still success because at least one was killed

    @pytest.mark.asyncio
    async def test_deadlock_retry_decorator_success_after_retry(self):
        """Test deadlock retry decorator successfully retries after deadlock."""
        call_count = 0

        @deadlock_retry(max_retries=3, base_delay=0.1)
        async def test_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise DeadlockDetectedError("Simulated deadlock")
            return "success"

        with patch('dags.data_pipeline.utility_code.handle_deadlock_with_retry') as mock_handler:
            mock_handler.return_value = {'deadlocks_found': 1, 'deadlocks_killed': 1, 'success': True}

            result = await test_function()

            assert result == "success"
            assert call_count == 2
            mock_handler.assert_called_once()

    @pytest.mark.asyncio
    async def test_deadlock_retry_decorator_max_retries_exceeded(self):
        """Test deadlock retry decorator fails after max retries."""
        call_count = 0

        @deadlock_retry(max_retries=2, base_delay=0.1)
        async def test_function():
            nonlocal call_count
            call_count += 1
            raise DeadlockDetectedError("Persistent deadlock")

        with patch('dags.data_pipeline.utility_code.handle_deadlock_with_retry') as mock_handler:
            mock_handler.return_value = {'deadlocks_found': 0, 'deadlocks_killed': 0, 'success': True}

            with pytest.raises(MaxRetriesExceededError):
                await test_function()

            assert call_count == 2  # Should try max_retries times

    @pytest.mark.asyncio
    async def test_deadlock_retry_decorator_non_deadlock_error(self):
        """Test deadlock retry decorator doesn't retry non-deadlock errors."""
        call_count = 0

        @deadlock_retry(max_retries=3, base_delay=0.1)
        async def test_function():
            nonlocal call_count
            call_count += 1
            raise ValueError("Not a deadlock")

        with pytest.raises(ValueError):
            await test_function()

        assert call_count == 1  # Should not retry

    def test_deadlock_retry_decorator_sync_function(self):
        """Test deadlock retry decorator works with sync functions."""
        call_count = 0

        @deadlock_retry(max_retries=3, base_delay=0.1)
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise DeadlockDetected("Simulated sync deadlock")
            return "success"

        result = test_function()

        assert result == "success"
        assert call_count == 2

    @pytest.mark.asyncio
    async def test_upsert_async_deadlock_handling_integration(self):
        """Integration test for deadlock handling in upsert_async."""
        import pandas as pd
        from unittest.mock import AsyncMock
        from sqlalchemy.ext.asyncio import AsyncSession
        from dags.data_pipeline.utility_code import upsert_async

        # Create mock session and model
        mock_session = AsyncMock(spec=AsyncSession)
        mock_model = MagicMock()
        mock_model.__table__ = MagicMock()
        mock_model.__table__.name = "test_table"

        # Create test data
        test_df = pd.DataFrame({'id': [1, 2], 'name': ['test1', 'test2']})

        # Mock the execute method to raise deadlock on first call, succeed on second
        call_count = 0
        async def mock_execute(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise DeadlockDetectedError("Simulated deadlock in upsert")
            return MagicMock()

        mock_session.execute = mock_execute
        mock_session.commit = AsyncMock()

        with patch('dags.data_pipeline.utility_code.handle_deadlock_with_retry') as mock_handler:
            mock_handler.return_value = {'deadlocks_found': 1, 'deadlocks_killed': 1, 'success': True}

            # Should succeed after retry
            await upsert_async(
                session=mock_session,
                model=mock_model,
                rows=test_df,
                max_retries=3,
                retry_delay=0.1
            )

            assert call_count == 2  # First call failed, second succeeded
            mock_handler.assert_called_once()


class TestDeadlockDetectionAndKilling:
    """Test deadlock detection and killing functions."""

    @pytest.mark.asyncio
    async def test_detect_deadlocks_success(self):
        """Test successful deadlock detection."""
        with patch('dags.data_pipeline.utility_code.ApplicationContainer') as mock_container:
            mock_session = AsyncMock()
            mock_result = MagicMock()
            mock_result.fetchall.return_value = [(12345,), (67890,)]
            mock_session.execute.return_value = mock_result

            mock_db = MagicMock()
            mock_db.update_schema.return_value.async_session.return_value.__aenter__.return_value = mock_session
            mock_container.return_value.database_rw.return_value = mock_db

            pids = await detect_deadlocks()

            assert pids == [12345, 67890]

    @pytest.mark.asyncio
    async def test_kill_transaction_success(self):
        """Test successful transaction killing."""
        with patch('dags.data_pipeline.utility_code.ApplicationContainer') as mock_container:
            mock_session = AsyncMock()
            mock_session.execute.return_value = None

            mock_db = MagicMock()
            mock_db.update_schema.return_value.async_session.return_value.__aenter__.return_value = mock_session
            mock_container.return_value.database_rw.return_value = mock_db

            # Should not raise any exception
            await kill_transaction(12345)

            mock_session.execute.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--allure-features"])
