#!/usr/bin/env python3
# coding=utf-8
"""
Test script to demonstrate the enhanced error handling and graceful shutdown functionality.
This script simulates database connection failures and shows how the system handles them.
"""

import asyncio
import io
import logging
import sys
import os

import pandas as pd
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import InterfaceError

# CRITICAL: Set environment variable FIRST, before any output operations
os.environ['PYTHONIOENCODING'] = 'utf-8'


def setup_utf8_output():
    """Setup UTF-8 encoding for stdout and stderr on Windows"""
    try:
        # Check if we're on Windows and need to wrap streams
        if sys.platform.startswith('win') and hasattr(sys.stdout, 'detach'):
            # Only wrap if not already wrapped and if encoding is not utf-8
            if not hasattr(sys.stdout, 'buffer') or sys.stdout.encoding.lower() != 'utf-8':
                sys.stdout = io.TextIOWrapper(
                    sys.stdout.detach(),
                    encoding='utf-8',
                    errors='replace'  # This prevents crashes on encoding errors
                )
            if not hasattr(sys.stderr, 'buffer') or sys.stderr.encoding.lower() != 'utf-8':
                sys.stderr = io.TextIOWrapper(
                    sys.stderr.detach(),
                    encoding='utf-8',
                    errors='replace'
                )
    except (AttributeError, OSError):
        # Fallback: streams might already be wrapped or unavailable
        pass


# Call this BEFORE any logging setup
setup_utf8_output()


# Custom Logging Formatter with Fallback
class SafeUnicodeFormatter(logging.Formatter):
    """Custom formatter that handles Unicode characters safely"""

    def format(self, record):
        try:
            return super().format(record)
        except UnicodeEncodeError:
            # Fallback: replace problematic characters
            msg = super().format(record)
            return msg.encode('ascii', errors='replace').decode('ascii')


# Safe logging function
def safe_log(logger_inst, level, message):
    """Safely log a message with Unicode fallback"""
    try:
        getattr(logger_inst, level)(message)
    except UnicodeEncodeError:
        # Replace Unicode characters with ASCII equivalents
        safe_message = message.encode('ascii', errors='replace').decode('ascii')
        getattr(logger_inst, level)(safe_message)


# Unicode fallback dictionary
UNICODE_FALLBACKS = {
    '✅': '[OK]',
    '❌': '[FAIL]',
    '⚠️': '[WARN]',
    '🔍': '[SEARCH]',
    '📝': '[NOTE]',
    '🚀': '[START]',
    '⏱️': '[TIME]',
    '💡': '[INFO]',
}


def safe_unicode(text: str) -> str:
    """Replace Unicode symbols with ASCII fallbacks if needed"""
    for unicode_char, fallback in UNICODE_FALLBACKS.items():
        text = text.replace(unicode_char, fallback)
    return text


# Setup logging with proper Unicode handling
def setup_logging():
    """Setup logging with Unicode support"""
    # Clear any existing handlers to avoid conflicts
    logging.getLogger().handlers.clear()

    # Create handler with UTF-8 support
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(SafeUnicodeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[handler],
        force=True  # Force reconfiguration
    )


# Call logging setup
setup_logging()
logger = logging.getLogger(__name__)

# Import the enhanced functions
try:
    from utility_code import (
        upsert_async_with_health_check,
        upsert_async,
        GracefulShutdownRequested,
        DatabaseConnectionError,
        MaxRetriesExceededError,
        is_shutdown_requested,
        request_graceful_shutdown
    )
except ImportError as e:
    logger.error(f"Failed to import utility_code: {e}")


    # Create mock classes for testing if import fails
    class GracefulShutdownRequested(Exception):
        pass


    class DatabaseConnectionError(Exception):
        pass


    class MaxRetriesExceededError(Exception):
        pass


    def is_shutdown_requested():
        return False


    def request_graceful_shutdown(msg, logger):
        raise GracefulShutdownRequested(msg)


    async def upsert_async(*args, **kwargs):
        raise NotImplementedError("Mock function")


    async def upsert_async_with_health_check(*args, **kwargs):
        raise NotImplementedError("Mock function")


class MockModel:
    """Mock SQLAlchemy model for testing"""
    __table__ = MagicMock()
    __table__.name = "test_table"


async def test_connection_timeout_error():
    """Test handling of connection timeout errors"""
    safe_log(logger, 'info', "=== Testing Connection Timeout Error ===")

    # Create mock session that raises TimeoutError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = TimeoutError("Connection timed out")

    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=3,
            retry_delay=0.1,  # Fast retry for testing
            my_logger=logger
        )
        safe_log(logger, 'error', "Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        safe_log(logger, 'info', safe_unicode(f"✅ Graceful shutdown correctly requested: {e}"))
    except Exception as e:
        safe_log(logger, 'error', safe_unicode(f"❌ Unexpected exception: {e}"))


async def test_cancelled_error():
    """Test handling of CancelledError (asyncio cancellation)"""
    safe_log(logger, 'info', "=== Testing Cancelled Error ===")

    # Create mock session that raises CancelledError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = asyncio.CancelledError("Operation was cancelled")

    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=3,
            retry_delay=0.1,
            my_logger=logger
        )
        safe_log(logger, 'error', "Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        safe_log(logger, 'info', safe_unicode(f"✅ Graceful shutdown correctly requested: {e}"))
    except Exception as e:
        safe_log(logger, 'error', safe_unicode(f"❌ Unexpected exception: {e}"))


async def test_interface_error():
    """Test handling of SQLAlchemy InterfaceError"""
    safe_log(logger, 'info', "=== Testing Interface Error ===")

    # Create mock session that raises InterfaceError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = InterfaceError("Connection is closed", None, None)

    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=2,
            retry_delay=0.1,
            my_logger=logger
        )
        safe_log(logger, 'error', "Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        safe_log(logger, 'info', safe_unicode(f"✅ Graceful shutdown correctly requested: {e}"))
    except Exception as e:
        safe_log(logger, 'error', safe_unicode(f"❌ Unexpected exception: {e}"))


async def test_health_check_wrapper():
    """Test the health check wrapper function"""
    safe_log(logger, 'info', "=== Testing Health Check Wrapper ===")

    # Create mock session that fails health check
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = Exception("Health check failed")

    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

    try:
        await upsert_async_with_health_check(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            my_logger=logger
        )
        safe_log(logger, 'error', "Expected DatabaseConnectionError but function completed normally")
    except DatabaseConnectionError as e:
        safe_log(logger, 'info', safe_unicode(f"✅ Database connection error correctly detected: {e}"))
    except GracefulShutdownRequested as e:
        safe_log(logger, 'info', safe_unicode(f"✅ Graceful shutdown correctly requested: {e}"))
    except Exception as e:
        safe_log(logger, 'error', safe_unicode(f"❌ Unexpected exception: {e}"))


async def test_successful_operation():
    """Test successful operation without errors"""
    safe_log(logger, 'info', "=== Testing Successful Operation ===")

    # Create mock session that succeeds
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.return_value = MagicMock()

    # Mock the health check to succeed
    mock_result = AsyncMock()
    mock_result.fetchone.return_value = (1,)
    mock_session.execute.return_value = mock_result

    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})

    try:
        await upsert_async_with_health_check(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            my_logger=logger
        )
        safe_log(logger, 'info', safe_unicode("✅ Successful operation completed without errors"))
    except Exception as e:
        safe_log(logger, 'error', safe_unicode(f"❌ Unexpected exception in successful operation: {e}"))


async def test_shutdown_flag():
    """Test the global shutdown flag functionality"""
    safe_log(logger, 'info', "=== Testing Shutdown Flag ===")

    # Reset shutdown flag
    try:
        import utility_code
        utility_code._shutdown_requested = False
    except:
        pass  # Mock implementation

    # Check initial state
    initial_state = is_shutdown_requested()
    safe_log(logger, 'info', f"Initial shutdown flag state: {initial_state}")

    # Request shutdown
    try:
        request_graceful_shutdown("Test shutdown request", logger)
        safe_log(logger, 'error', "Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested:
        safe_log(logger, 'info', safe_unicode("✅ Graceful shutdown correctly requested"))

    # Check flag is now set
    final_state = is_shutdown_requested()
    safe_log(logger, 'info', f"Final shutdown flag state: {final_state}")


async def main():
    """Run all tests"""
    safe_log(logger, 'info', "Starting enhanced error handling tests...")

    tests = [
        test_shutdown_flag,
        test_successful_operation,
        test_health_check_wrapper,
        test_connection_timeout_error,
        test_cancelled_error,
        test_interface_error,
    ]

    for test in tests:
        try:
            await test()
            safe_log(logger, 'info', safe_unicode(f"✅ {test.__name__} completed"))
        except Exception as e:
            safe_log(logger, 'error', safe_unicode(f"❌ {test.__name__} failed: {e}"))

        # Add a small delay between tests
        await asyncio.sleep(0.1)
        safe_log(logger, 'info', "-" * 50)

    safe_log(logger, 'info', "All tests completed!")


if __name__ == "__main__":
    # Test the Unicode handling
    try:
        safe_log(logger, 'info', safe_unicode("✅ Unicode test - this should work"))
        print(safe_unicode("✅ Direct print test"))
    except Exception as e:
        print(f"Unicode test failed: {e}")

    # Run the main tests
    asyncio.run(main())