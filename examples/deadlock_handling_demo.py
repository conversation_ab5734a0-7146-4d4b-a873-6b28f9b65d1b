#!/usr/bin/env python3
# coding=utf-8
"""
Demonstration of deadlock handling capabilities.

This script shows how to use the enhanced deadlock handling system
without requiring the full application dependencies.
"""

import asyncio
import logging
import time
from typing import Dict, Any

# Mock the deadlock exceptions for demonstration
class MockDeadlockDetectedError(Exception):
    """Mock async deadlock exception"""
    pass

class MockDeadlockDetected(Exception):
    """Mock sync deadlock exception"""
    pass

# Mock deadlock detection and killing functions
async def mock_detect_deadlocks() -> list[int]:
    """Mock deadlock detection that simulates finding deadlocks"""
    # Simulate finding some deadlocked PIDs
    return [12345, 67890]

async def mock_kill_transaction(pid: int) -> None:
    """Mock transaction killing"""
    print(f"  🔪 Killed transaction PID: {pid}")
    await asyncio.sleep(0.1)  # Simulate time to kill

async def mock_handle_deadlock_with_retry(operation_name: str) -> Dict[str, Any]:
    """
    Mock enhanced deadlock handler that detects, kills, and provides retry context.
    """
    print(f"  🔍 Detecting deadlocks for operation: {operation_name}")
    
    try:
        # Detect deadlocked transactions
        deadlocked_pids = await mock_detect_deadlocks()
        
        if deadlocked_pids:
            print(f"  ⚠️  Found {len(deadlocked_pids)} deadlocked transactions: {deadlocked_pids}")
            
            # Kill deadlocked transactions
            killed_count = 0
            for pid in deadlocked_pids:
                try:
                    await mock_kill_transaction(pid)
                    killed_count += 1
                except Exception as kill_error:
                    print(f"  ❌ Failed to kill transaction {pid}: {kill_error}")
            
            print(f"  ✅ Successfully killed {killed_count}/{len(deadlocked_pids)} deadlocked transactions")
            
            return {
                'deadlocks_found': len(deadlocked_pids),
                'deadlocks_killed': killed_count,
                'success': killed_count > 0
            }
        else:
            print(f"  ✅ No deadlocks detected for {operation_name}")
            return {
                'deadlocks_found': 0,
                'deadlocks_killed': 0,
                'success': True
            }
            
    except Exception as e:
        print(f"  ❌ Error in deadlock handling for {operation_name}: {e}")
        return {
            'deadlocks_found': 0,
            'deadlocks_killed': 0,
            'success': False,
            'error': str(e)
        }

def mock_deadlock_retry(max_retries: int = 3, base_delay: float = 2.0):
    """
    Mock deadlock retry decorator for demonstration.
    """
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    print(f"  🔄 Attempt {attempt + 1}/{max_retries} for {func.__name__}")
                    return await func(*args, **kwargs)
                    
                except MockDeadlockDetectedError as e:
                    last_error = e
                    attempt_num = attempt + 1
                    
                    print(f"  💥 Deadlock detected in {func.__name__} (attempt {attempt_num}/{max_retries}): {e}")
                    
                    if attempt < max_retries - 1:  # Not the last attempt
                        # Try to resolve deadlock
                        deadlock_result = await mock_handle_deadlock_with_retry(func.__name__)
                        if deadlock_result['deadlocks_found'] > 0:
                            print(f"  🔧 Deadlock resolution: {deadlock_result}")
                        
                        # Calculate delay with exponential backoff
                        delay = base_delay * (2 ** attempt)
                        print(f"  ⏳ Retrying {func.__name__} in {delay:.2f} seconds...")
                        await asyncio.sleep(delay)
                    else:
                        print(f"  ❌ Max deadlock retries ({max_retries}) exceeded for {func.__name__}")
                        break
                        
                except Exception as e:
                    # For non-deadlock errors, re-raise immediately
                    print(f"  ❌ Non-deadlock error in {func.__name__}: {e}")
                    raise
            
            # All retries exhausted
            raise Exception(f"Function {func.__name__} failed after {max_retries} deadlock retry attempts") from last_error
        
        return async_wrapper
    return decorator

# Demonstration functions
@mock_deadlock_retry(max_retries=3, base_delay=1.0)
async def mock_database_operation_success_after_retry():
    """Simulates a database operation that fails once with deadlock, then succeeds"""
    if not hasattr(mock_database_operation_success_after_retry, 'call_count'):
        mock_database_operation_success_after_retry.call_count = 0
    
    mock_database_operation_success_after_retry.call_count += 1
    
    if mock_database_operation_success_after_retry.call_count == 1:
        raise MockDeadlockDetectedError("Simulated deadlock on first attempt")
    
    return "Database operation completed successfully!"

@mock_deadlock_retry(max_retries=2, base_delay=0.5)
async def mock_database_operation_persistent_deadlock():
    """Simulates a database operation that always deadlocks"""
    raise MockDeadlockDetectedError("Persistent deadlock - this operation always fails")

async def mock_upsert_with_deadlock_handling():
    """Simulates the enhanced upsert_async with deadlock handling"""
    print("🔄 Starting mock upsert operation...")
    
    max_retries = 3
    retry_delay = 1.0
    
    for attempt in range(max_retries):
        try:
            print(f"  📝 Upsert attempt {attempt + 1}/{max_retries}")
            
            # Simulate deadlock on first attempt
            if attempt == 0:
                raise MockDeadlockDetectedError("Deadlock detected in upsert for table issue_links")
            
            # Simulate success on retry
            print("  ✅ Upsert completed successfully!")
            return
            
        except MockDeadlockDetectedError as e:
            print(f"  💥 Deadlock error: {e}")
            
            if attempt < max_retries - 1:
                print("  🔧 Handling deadlock...")
                
                # Use the enhanced deadlock handler
                deadlock_result = await mock_handle_deadlock_with_retry(f"upsert_issue_links")
                
                if deadlock_result['success']:
                    if deadlock_result['deadlocks_found'] > 0:
                        print(f"  ✅ Deadlock resolution successful: killed {deadlock_result['deadlocks_killed']}/{deadlock_result['deadlocks_found']} deadlocked transactions")
                    else:
                        print("  ℹ️  No active deadlocks found, deadlock may have resolved naturally")
                else:
                    print(f"  ⚠️  Deadlock resolution failed: {deadlock_result.get('error', 'Unknown error')}")
                
                # Wait before retry with exponential backoff
                wait_time = retry_delay * (2 ** attempt) + (retry_delay * 0.2 * attempt)
                print(f"  ⏳ Retrying in {wait_time:.2f} seconds...")
                await asyncio.sleep(wait_time)
            else:
                print("  ❌ Max retries reached for deadlock error")
                raise

async def demonstrate_deadlock_handling():
    """Main demonstration function"""
    print("🚀 Deadlock Handling Demonstration")
    print("=" * 50)
    
    # Demo 1: Successful retry after deadlock
    print("\n📋 Demo 1: Database operation with successful deadlock retry")
    print("-" * 50)
    try:
        result = await mock_database_operation_success_after_retry()
        print(f"✅ Result: {result}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Demo 2: Max retries exceeded
    print("\n📋 Demo 2: Database operation with persistent deadlocks")
    print("-" * 50)
    try:
        result = await mock_database_operation_persistent_deadlock()
        print(f"✅ Result: {result}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Demo 3: Upsert with deadlock handling (like your real scenario)
    print("\n📋 Demo 3: Upsert operation with deadlock handling")
    print("-" * 50)
    try:
        await mock_upsert_with_deadlock_handling()
        print("✅ Upsert completed successfully!")
    except Exception as e:
        print(f"❌ Upsert failed: {e}")
    
    print("\n🎉 Demonstration completed!")
    print("\nKey Features Demonstrated:")
    print("• Automatic deadlock detection")
    print("• Deadlocked transaction killing")
    print("• Exponential backoff retry logic")
    print("• Comprehensive error handling")
    print("• Integration with existing upsert operations")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Run the demonstration
    asyncio.run(demonstrate_deadlock_handling())
