# Deadlock Handling Guide

This guide explains how to use the enhanced deadlock handling system in the data pipeline.

## Overview

The system provides automatic deadlock detection, killing, and retry mechanisms to handle PostgreSQL deadlocks gracefully. When a deadlock is detected, the system can:

1. **Detect** deadlocked transactions using PostgreSQL system tables
2. **Kill** deadlocked transactions using `pg_terminate_backend`
3. **Retry** the failed operation automatically
4. **Monitor** for deadlocks continuously in the background

## Key Components

### 1. Deadlock Detection (`detect_deadlocks`)

```python
deadlocked_pids = await detect_deadlocks()
# Returns list of PIDs involved in deadlocks: [12345, 67890]
```

### 2. Transaction Killing (`kill_transaction`)

```python
await kill_transaction(pid=12345)
# Terminates the transaction with the given PID
```

### 3. Enhanced Deadlock Handler (`handle_deadlock_with_retry`)

```python
result = await handle_deadlock_with_retry("operation_name")
# Returns: {
#     'deadlocks_found': 2,
#     'deadlocks_killed': 2,
#     'success': True
# }
```

### 4. Deadlock Retry Decorator (`@deadlock_retry`)

```python
@deadlock_retry(max_retries=3, base_delay=2.0)
async def my_database_operation():
    # Your database code here
    pass
```

## Usage Examples

### Automatic Deadlock Handling in upsert_async

The `upsert_async` function now automatically handles deadlocks:

```python
await upsert_async(
    session=session,
    model=MyModel,
    rows=dataframe,
    max_retries=5,  # Will retry deadlocks up to 5 times
    retry_delay=1.0  # Base delay between retries
)
```

When a deadlock occurs:
1. The error is detected as a deadlock
2. The system attempts to find and kill deadlocked transactions
3. The operation waits with exponential backoff
4. The operation is retried automatically

### Using the Deadlock Retry Decorator

For custom database operations:

```python
@deadlock_retry(max_retries=3, base_delay=2.0, enable_deadlock_killing=True)
async def custom_database_operation(session):
    # Your database operations that might deadlock
    result = await session.execute(complex_query)
    await session.commit()
    return result

# Usage
try:
    result = await custom_database_operation(session)
except MaxRetriesExceededError:
    logger.error("Operation failed after all deadlock retries")
```

### Manual Deadlock Handling

For fine-grained control:

```python
async def my_operation():
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Your database operation
            await perform_database_work()
            break
        except DeadlockDetectedError:
            if attempt < max_retries - 1:
                # Handle the deadlock
                result = await handle_deadlock_with_retry("my_operation")
                if result['success']:
                    logger.info(f"Resolved deadlock: {result}")
                
                # Wait before retry
                await asyncio.sleep(2.0 * (2 ** attempt))
            else:
                raise
```

### Background Deadlock Monitoring

Start continuous deadlock monitoring:

```python
# Start background monitoring (checks every 5 seconds)
monitor_task = asyncio.create_task(monitor_deadlocks(interval=5))

# Later, cancel monitoring
monitor_task.cancel()
```

## Configuration Options

### upsert_async Deadlock Settings

```python
await upsert_async(
    session=session,
    model=Model,
    rows=df,
    max_retries=5,           # Total retries for all errors
    retry_delay=1.0,         # Base delay between retries
    enable_graceful_shutdown=True  # Shutdown on max retries
)
```

### Deadlock Retry Decorator Settings

```python
@deadlock_retry(
    max_retries=3,              # Max deadlock-specific retries
    base_delay=2.0,             # Base delay between retries
    max_delay=30.0,             # Maximum delay cap
    enable_deadlock_killing=True # Whether to kill deadlocks
)
```

## Error Handling

### Retryable vs Non-Retryable Errors

The system distinguishes between different error types:

**Retryable (Deadlock) Errors:**
- `asyncpg.exceptions.DeadlockDetectedError`
- `psycopg2.errors.DeadlockDetected`
- Any error containing "deadlock" in the message

**Retryable (Connection) Errors:**
- Connection timeouts
- Connection drops
- Network errors

**Non-Retryable Errors:**
- Data validation errors
- Constraint violations (non-deadlock)
- Application logic errors

### Custom Exception Handling

```python
from dags.data_pipeline.utility_code import MaxRetriesExceededError

try:
    await my_deadlock_prone_operation()
except MaxRetriesExceededError as e:
    logger.error(f"All deadlock retries exhausted: {e}")
    # Handle graceful degradation
except DeadlockDetectedError as e:
    logger.error(f"Unhandled deadlock: {e}")
    # This shouldn't happen with proper retry handling
```

## Best Practices

### 1. Use Appropriate Retry Counts
- **Light operations**: 3-5 retries
- **Heavy operations**: 2-3 retries
- **Critical operations**: Consider manual handling

### 2. Set Reasonable Delays
- **Base delay**: 1-3 seconds
- **Max delay**: 30-60 seconds
- **Exponential backoff**: Automatically applied

### 3. Monitor Deadlock Patterns
```python
# Enable debug monitoring to track deadlock patterns
from dags.data_pipeline.debug_utils import enable_debug_monitoring
enable_debug_monitoring()
```

### 4. Handle Graceful Shutdown
```python
# Enable graceful shutdown on persistent failures
await upsert_async(
    session=session,
    model=Model,
    rows=df,
    enable_graceful_shutdown=True
)
```

### 5. Log Deadlock Events
All deadlock events are automatically logged with:
- Transaction PIDs involved
- Number of deadlocks found/killed
- Retry attempt information
- Resolution success/failure

## Troubleshooting

### High Deadlock Frequency
If you see frequent deadlocks:

1. **Check transaction ordering**: Ensure consistent lock acquisition order
2. **Reduce transaction scope**: Break large transactions into smaller ones
3. **Increase retry delays**: Give more time between retries
4. **Monitor lock contention**: Use PostgreSQL lock monitoring queries

### Deadlock Killing Failures
If deadlock killing fails:

1. **Check permissions**: Ensure database user can terminate backends
2. **Monitor system load**: High load may prevent successful termination
3. **Increase timeouts**: Allow more time for termination

### Performance Impact
The deadlock handling system is designed to be lightweight:

- **Detection queries**: Fast, use system indexes
- **Killing operations**: Minimal overhead
- **Retry logic**: Only activates on actual deadlocks

## Testing

Run the deadlock handling tests:

```bash
pytest tests/deadlock_handling/test_deadlock_retry.py -v
```

The tests cover:
- Deadlock detection and killing
- Retry decorator functionality
- Integration with upsert_async
- Error handling scenarios
